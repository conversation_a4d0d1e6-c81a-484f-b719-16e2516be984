<template>
  <!-- 404错误页面 -->
  <div v-if="is404Error">
    <div v-if="!isMobile" class="error-404-wrapper w-full overflow-hidden">
      <div class="error-404-container">
        <div class="w-[2266px] h-[584px] relative">
          <img
            alt="404"
            loading="lazy"
            class="w-[2266px] h-[584px]"
            src="@/assets/icons/common/404.svg"
          />
          <div
            class="absolute top-[272px] right-[606px] flex flex-col items-center gap-[70px]"
          >
            <p class="text-[20px] leading-[20px]">
              Lo sentimos, la página que intenta acceder no existe
            </p>
            <a
              href="/"
              data-spm-box="notfound-gohome-button"
              class="inline-block py-[27px] px-[31px] text-[20px] leading-[20px] text-[#fff] text-center rounded-[500px] bg-[#e50113] hover:bg-[#F20114] transition-all duration-300 cursor-pointer"
            >
              Volver al inicio
            </a>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <div class="error-404-container-mobile">
        <img
          alt="404"
          loading="lazy"
          class="w-full"
          src="@/assets/icons/common/404-mobile.svg"
        />
        <div
          class="w-full absolute top-[35%] flex flex-col items-center gap-[40px] px-[20px]"
        >
          <p class="text-[18px] leading-[32px] text-center">
            Lo sentimos, la página que intenta<br />
            acceder no existe
          </p>
          <a
            href="/h5"
            data-spm-box="notfound-gohome-button"
            class="inline-block py-[21px] px-[29px] text-[18px] leading-[18px] text-[#e50113] text-center rounded-[500px] border-1 border-[#e50113] cursor-pointer"
          >
            Volver al inicio
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- 其他错误页面 -->
  <div v-else class="error-wrapper">
    <img
      alt="chilat"
      loading="lazy"
      class="error-image"
      src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/04/10/698a0418-31ed-49e9-b8fb-3ef4f14a0c5b.svg"
    />
    <div class="error-message">
      El sistema está en mantenimiento. Por favor, inténtelo de nuevo en cinco
      minutos.
    </div>
  </div>
</template>

<script setup lang="ts">
const { isMobile } = useDevice();
const props = defineProps({
  error: Object,
});

// 判断是否为404错误
const is404Error = computed(() => {
  return props.error?.statusCode === 404;
});

watch(
  is404Error,
  (newValue) => {
    if (newValue) {
      useHead({
        title: "404 - Página no encontrada - Chilat",
      });
    }
  },
  { immediate: true }
);

// 只有非404错误才执行自动刷新逻辑
onMounted(() => {
  if (!is404Error.value) {
    const url = new URL(window.location.href);
    const reloadTimes = Number(url.searchParams.get("reload_times") || 0) + 1;
    url.searchParams.set("reload_times", String(reloadTimes));
    setTimeout(() => {
      window.location.href = url.toString();
    }, 60000);
  }
});
</script>

<style scoped lang="scss">
.error-404-wrapper {
  min-width: 1280px;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  background: linear-gradient(
    to bottom,
    #fff 0,
    #fff 515px,
    #ccc 515px,
    #ccc 100%
  );
}
.error-404-container-mobile {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(
    to bottom,
    #fff 0,
    #fff 632px,
    #ccc 632px,
    #ccc 100%
  );
}

// 其他错误页面样式
.error-wrapper {
  min-height: 100vh;
  padding: 20vh 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.error-image {
  width: 100%;
  max-width: 580px;
  height: auto;
}

.error-message {
  font-size: 24px;
  line-height: 1.6;
  margin-top: 40px;
  color: #333;
}

@media (max-width: 1280px) {
  .error-image {
    max-width: 435px;
  }

  .error-message {
    font-size: 18px;
    line-height: 30px;
  }
}

@media (max-width: 750px) {
  .error-wrapper {
    max-width: 435px;
    margin: 0 auto;
    justify-content: center;
    padding-top: 0;
  }

  .error-message {
    font-size: 18px;
    line-height: 32px;
  }
}
</style>
