<template>
  <a
    :href="pageData.linkUrl"
    @click="onLinkTo"
    :data-spm-box="props.pageSource"
    :data-spm-index="props.goodsIndex + 1"
    :data-spm-param="route?.query?.tagId || null"
  >
    <n-card
      :bordered="false"
      class="px-[0.08rem] hover:cursor-pointer text-black"
      :class="
        props.from === 'search'
          ? 'search-product-card'
          : 'category-product-card'
      "
    >
      <div class="flex w-full">
        <n-image
          lazy
          preview-disabled
          class="goods-image"
          :src="goods.mainImageUrl"
          :alt="goods.goodsName"
          :img-props="{ referrerpolicy: 'no-referrer' }"
        >
        </n-image>
        <n-space vertical :style="{ gap: '0.08rem 0' }" class="w-full">
          <n-ellipsis
            :line-clamp="2"
            class="goods-name"
            :tooltip="false"
            v-if="pageData.type !== 'imgSearch'"
          >
            {{ goods.goodsName }}
          </n-ellipsis>
          <template v-if="pageData.type !== 'imgSearch'">
            <div class="goods-price">
              <!-- 规格单价的最小值和最大值展示；若相同则只展示一个价格值 -->
              <!-- <span v-if="goods.minPrice != goods.maxPrice"
                >{{ setUnit(goods.minPrice) }} - {{ goods.maxPrice }}</span
              >
              <span v-else>{{ setUnit(goods.maxPrice) }}</span> -->
              <span>{{ setUnit(goods.minPrice) }}</span>
              <span class="goods-unit">/ {{ goods.goodsPriceUnitName }}</span>
            </div>
            <div
              v-if="goods.pcsEstimateFreight"
              :class="props.from === 'search' ? 'flex flex-wrap gap-[0.08rem]' : ''"
              class="text-[0.24rem] leading-[0.32rem] text-[#000]"
            >
              <div v-if="goods?.supportOnlineOrder">
                {{ authStore.i18n("cm_goods.finalShippingCost") }}:
              </div>
              <div v-else>{{ authStore.i18n("cm_goods.shippingCost") }}:</div>
              {{ setUnit(goods.pcsEstimateFreight) }}
              /
              {{ goods?.goodsPriceUnitName }}
            </div>
          </template>

          <div class="flex flex-col justify-between goods-buy">
            <!-- <div
              class="flex-1 goods-minify"
              v-if="pageData.type !== 'imgSearch'"
            >
              <span
                >{{ goods.minBuyQuantity }} {{ goods.goodsPriceUnitName }}</span
              >
              <span class="goods-minify-desc">(MOQ)</span>
            </div> -->
            <div v-if="pageData.type === 'imgSearch'" class="goods-min-price">
              {{ authStore.i18n("cm_goods_atLeast") }}
              {{ setUnit(goods.minPrice) }}
            </div>
            <icon-card
              :size="props.from === 'search' ? '0.6rem' : '0.48rem'"
              name="f7:cart"
              :color="goods.selected ? '#E50113' : '#858585'"
              @click.stop.prevent="onAddCart($event)"
              class="ml-auto"
            ></icon-card>
          </div>
        </n-space>
      </div>
    </n-card>
  </a>
</template>

<script setup lang="ts" name="MobileProductCard">
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const authStore = useAuthStore();
const emit = defineEmits([
  "onOpenDetail",
  "onUpdateGoodsId",
  "onUpdateLoading",
]);

const props = defineProps({
  goods: {
    type: Object,
    default: () => {},
  },
  discountRate: {
    type: String,
    default: "",
  },
  pageSource: {
    type: String,
    default: "",
  },
  goodsIndex: {
    type: Number,
    default: 1,
  },
  from: {
    type: String,
    default: "search",
  },
});

const pageData = reactive(<any>{
  type: route.query.type || "",
  linkUrl: "",
});

// 获取商品详情链接
const getLinkUrl = async (): Promise<string | null> => {
  let goodsId = props.goods.goodsId;
  if (!goodsId) {
    emit("onUpdateLoading", true);
    const res: any = await useGetGoods({ str: props.goods.sourceGoodsId });
    emit("onUpdateLoading", false);

    if (res?.result?.code === 200) {
      goodsId = res?.data;
      emit("onUpdateGoodsId", props.goodsIndex, goodsId);
    } else {
      showToast(authStore.i18n("cm_common_addGoodsError"));
      return null;
    }
  }
  return `/h5/goods/${goodsId}`;
};

// 跳转详情页
const onLinkTo = async (event: any) => {
  event.preventDefault();
  const url = await getLinkUrl();
  if (!url) return;

  const params: Record<string, string> = {};
  if (props.goods.padc) params.padc = props.goods.padc;

  navigateToPage(url, params, false, event);

  // 埋点
  window?.MyStat?.addPageEvent?.(
    "click_goods_detail",
    `商品编码：${props.goods.goodsNo || "N/A"}`
  );
};

async function onAddCart(e: any) {
  emit("onOpenDetail", e, props.goodsIndex);
}
</script>

<style scoped lang="scss">
.n-card :deep(.n-card__content) {
  padding: 0rem 0.16rem 0.32rem 0.16rem;
}

.add-btn {
  width: 100%;
  height: 0.72rem;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.32rem;
  font-weight: 500;
  border-radius: 0.6rem;
  box-sizing: border-box;
  color: #2d2d2d;
  border: 0.02rem solid #464646;
  background: none;
  cursor: pointer;
  &:hover {
    background: linear-gradient(270deg, #fc573f, #e50113);
    color: #fff;
    border: none;
  }
  .add-btn-check {
    position: absolute;
    right: -0.12rem;
    top: -0.2rem;
    background-color: #fff;
    border-radius: 50%;
  }
}

.search-product-card {
  .goods-image {
    width: 2rem;
    height: 2rem;
    flex-shrink: 0;
    margin-right: 0.28rem;
  }
  .goods-name {
    height: 0.76rem;
    font-size: 0.28rem;
    line-height: 0.4rem;
  }
  .goods-price {
    font-size: 0.28rem;
    line-height: 0.4rem;
    font-weight: 500;
  }
  .goods-unit {
    font-weight: 400;
    margin-left: 0.08rem;
    color: rgb(156, 163, 175);
  }
  .goods-buy {
    font-size: 0.28rem;
    line-height: 0.4erm;
    margin-top: 0.02rem;
  }
  .goods-minify {
    margin-bottom: 0.16rem;
  }
  .goods-minify-desc {
    margin-left: 0.1rem;
    color: rgb(156, 163, 175);
  }
  .goods-min-price {
    font-size: 0.3rem;
    line-height: 0.4rem;
    font-weight: 600;
  }
}

.category-product-card {
  margin-bottom: 0.28rem;
  .goods-image {
    width: 1.4rem;
    height: 1.4rem;
    flex-shrink: 0;
    margin-right: 0.2rem;
  }
  .goods-name {
    height: 0.6rem;
    font-size: 0.24rem;
    line-height: 0.3rem;
  }
  .goods-price {
    font-size: 0.26rem;
    line-height: 0.3rem;
    font-weight: 500;
  }
  .goods-unit {
    font-weight: 400;
    margin-left: 0.08rem;
    color: rgb(156, 163, 175);
  }
  .goods-buy {
    font-size: 0.24rem;
    line-height: 0.24rem;
  }
  .goods-minify {
    margin-bottom: 0.1rem;
  }
  .goods-minify-desc {
    margin-left: 0.1rem;
    color: rgb(156, 163, 175);
  }

  :deep(.n-card__content) {
    padding: 0 !important;
  }
}
</style>
