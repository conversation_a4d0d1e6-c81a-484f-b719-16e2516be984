<template>
  <div class="article-wrapper">
    <mobile-search-bar></mobile-search-bar>
    <div
      class="text-[28px] leading-[0.64rem] text-[#333] font-medium mt-[0.64rem] px-[0.4rem]"
    >
      {{ pageData.articleDetail.title }}
    </div>
    <div
      data-spm-box="article-inner-link"
      v-if="pageData.articleDetail?.content"
      v-html="pageData.articleDetail?.content"
      class="whitespace-pre-wrap px-[0.4rem] mt-[0.64rem]"
    ></div>
    <mobile-page-footer></mobile-page-footer>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

const route = useRoute();
const authStore = useAuthStore();

// 设置SEO规范链接
setSeoCanonical();

const pageData = reactive<any>({
  articleDetail: "",
});

await onGetArticle();

// 设置SEO
useHead({
  title: `${pageData.articleDetail?.title} -ChilatShop`,
  meta: [
    {
      name: "description",
      content: sanitizeMetaDescription(pageData.articleDetail?.brief),
    },
  ],
});

async function onGetArticle() {
  const res: any = await useArticleDetail({
    id: route.query.id,
    title: route.query.title,
    articleCode: route.query.code,
  });
  if (res?.result?.code === 200 && res?.data) {
    pageData.articleDetail = res?.data;
  } else {
    throw createError({ statusCode: 404, statusMessage: "Not Found" });
  }
}
</script>
<style scoped lang="scss">
.article-wrapper {
  height: auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 100vh;
}
</style>
