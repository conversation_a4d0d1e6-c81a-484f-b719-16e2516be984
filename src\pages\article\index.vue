<template>
  <div class="container">
    <search-cate-card></search-cate-card>
    <div class="w-[740px] flex mx-auto">
      <div class="w-full">
        <div class="text-[28px] leading-[32px] text-[#333] mt-[44px]">
          {{ pageData.articleDetail.title }}
        </div>
        <!-- 文章内容 -->
        <div
          data-spm-box="article-inner-link"
          v-if="pageData.articleDetail?.content"
          v-html="pageData.articleDetail?.content"
          class="article-page whitespace-pre-wrap mt-[44px]"
        ></div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const authStore = useAuthStore();

// 设置SEO规范链接
setSeoCanonical();

const pageData = reactive<any>({
  articleDetail: "",
});

await onGetArticle();

// 设置SEO
useHead({
  title: `${pageData.articleDetail?.title} -ChilatShop`,
  meta: [
    {
      name: "description",
      content: sanitizeMetaDescription(pageData.articleDetail?.brief),
    },
  ],
});

async function onGetArticle() {
  const res: any = await useArticleDetail({
    id: route.query.id,
    title: route.query.title,
    articleCode: route.query.code,
  });

  if (res?.result?.code === 200 && res?.data) {
    pageData.articleDetail = res?.data;
  } else {
    throw createError({ statusCode: 404, statusMessage: "Not Found" });
  }
}
</script>
<style scoped lang="scss">
.container {
  height: auto;
  margin: 0 auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 100vh;
}
</style>
