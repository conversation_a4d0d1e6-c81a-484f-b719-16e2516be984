export default defineNuxtRouteMiddleware((to) => {
  // 只在客户端执行
  if (process.server) return;

  // 检测是否处于全屏状态
  function checkFullscreen() {
    return !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement
    );
  }

  // 如果处于全屏状态，不进行重定向
  if (checkFullscreen()) {
    return;
  }

  // 检查是否为404页面，如果是404则跳过设备跳转逻辑
  // 通过检查路由是否存在来判断是否会404
  const { $router } = useNuxtApp();
  const matchedRoute = $router.resolve(to.path);

  // 如果路由不存在或者会导致404，则跳过设备跳转
  if (!matchedRoute.matched || matchedRoute.matched.length === 0) {
    return;
  }

  // 设备检测：结合屏幕宽度和UserAgent
  const { isMobile } = useDevice();
  const width = Math.max(document.documentElement.clientWidth, 300);
  const isMobileByWidth = width < 600;

  // 优先使用UserAgent判断，屏幕宽度作为辅助
  const isMobileDevice = isMobile || isMobileByWidth;
  const isH5Page = to.path.startsWith("/h5");

  // 需要重定向的情况
  if ((isMobileDevice && !isH5Page) || (!isMobileDevice && isH5Page)) {
    // 使用统一的重定向逻辑处理特殊页面映射
    const { getRedirectUrl } = useDeviceRedirect();
    const currentPath = to.path;
    const routeInfo = {
      fullPath: to.fullPath,
      path: to.path,
      params: to.params,
      query: to.query,
    };

    const redirectUrl = getRedirectUrl(currentPath, isMobileDevice, routeInfo);

    // 执行重定向
    if (redirectUrl && redirectUrl !== to.fullPath) {
      window.location.href = redirectUrl;
    }
  }
});
