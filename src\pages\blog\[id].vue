<template>
  <div class="container">
    <search-cate-card></search-cate-card>
    <div class="w-[740px] flex mx-auto">
      <div class="w-full">
        <div class="breadcrumb text-[14px] leading-[14px] mt-[50px]">
          <div class="breadcrumb-item">
            <a href="/">
              <img
                loading="lazy"
                src="@/assets/icons/article/home-black.svg"
                alt="home"
                class="home-icon h-[14px]"
              />
            </a>
          </div>
          <img
            loading="lazy"
            src="@/assets/icons/article/arrow-right.svg"
            alt="arrow"
            class="arrow-icon"
          />
          <div class="breadcrumb-item">
            <a href="/blog">
              <div class="breadcrumb-link min-w-[30px] text-center">Blog</div>
            </a>
          </div>
          <img
            loading="lazy"
            src="@/assets/icons/article/arrow-right.svg"
            alt="arrow"
            class="arrow-icon"
          />
          <!-- 当前标题 -->
          <div class="breadcrumb-item">
            <a :href="`/blog/${pageData.articleDetail.articleCode}`">
              <span class="breadcrumb-link active max-w-[620px] line-clamp-1">
                {{ pageData.articleDetail.title }}
              </span>
            </a>
          </div>
        </div>
        <div class="text-[28px] leading-[32px] text-[#333] mt-[44px]">
          {{ pageData.articleDetail.title }}
        </div>
        <!-- 文章更新时间 -->
        <div
          v-if="pageData.articleDetail?.udate"
          class="flex flex-wrap gap-[4px] mt-[34px] text-[#7F7F7F] text-[14px] leading-[14px] italic"
        >
          <span>Etpublicado en </span>
          <span class="text-[#4290F7]">{{
            formatDateOnly(pageData.articleDetail.udate)
          }}</span>
        </div>
        <!-- 文章内容 -->
        <div
          data-spm-box="article-inner-link"
          v-if="pageData.articleDetail?.content"
          v-html="pageData.articleDetail?.content"
          class="article-page whitespace-pre-wrap mt-[44px]"
        ></div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { formatDateOnly } from "@/utils/date";
const route = useRoute();
const pageData = reactive<any>({
  articleDetail: "",
});

await onGetArticle();

// 设置SEO规范链接
setSeoCanonical();

useHead({
  title: `${pageData.articleDetail?.title} -Chilatshop blog`,
  meta: [
    {
      name: "description",
      content: sanitizeMetaDescription(pageData.articleDetail.brief),
    },
  ],
});

async function onGetArticle() {
  const res: any = await useArticleDetail({
    id: route.params.id,
    articleCode: route.params.id,
  });
  if (res?.result?.code === 200 && res?.data) {
    pageData.articleDetail = res?.data;
  } else {
    throw createError({ statusCode: 404, statusMessage: "Not Found" });
  }
}
</script>
<style scoped lang="scss">
.container {
  height: auto;
  margin: 0 auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 100vh;
}
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.home-icon:hover {
  content: url("@/assets/icons/article/home-red.svg");
}

.breadcrumb-link {
  color: #7f7f7f;
  transition: all 0.3s;
  cursor: pointer;
}

.breadcrumb-link:hover,
.breadcrumb-link.active {
  color: #333;
  font-weight: 500;
}

.arrow-icon {
  width: 6px;
  margin: 0 8px;
  filter: brightness(0) saturate(100%) invert(43%) sepia(0%) saturate(0%)
    hue-rotate(193deg) brightness(96%) contrast(90%);
}
</style>
