<template>
  <div class="goods-item px-[8px] pt-[8px] pb-[12px] rounded-[8px] bg-white">
    <a
      target="_blank"
      v-bind:href="`/goods/${goods.goodsId}${
        goods.padc ? `?padc=${goods.padc}` : ''
      }`"
      class="flex flex-col gap-[8px]"
    >
      <img
        loading="lazy"
        :src="goods.mainImageUrl"
        :alt="goods.goodsName"
        referrerpolicy="no-referrer"
        class="inline-block rounded-[4px] home-goods-image"
        :style="{ width: props.imageWidth, height: props.imageHeight }"
      />
      <p class="text-[14px] leading-[14px] break-all line-clamp-1">
        {{ goods.goodsName }}
      </p>
      <p class="text-[20px] leading-[20px] font-medium">
        <!-- 规格单价的最小值和最大值展示；若相同则只展示一个价格值 -->
        <!-- <span v-if="goods.minPrice != goods.maxPrice"
          >{{ setUnit(goods.minPrice) }} - {{ goods.maxPrice }}</span
        >
        <span v-else>{{ setUnit(goods.maxPrice) }}</span> -->
        <span>{{ setUnit(goods.minPrice) }}</span>
      </p>
      <div
        v-if="goods.pcsEstimateFreight"
        class="text-[12px] text-left bg-[#FAFAFA] rounded-tl-[8px] rounded-br-[8px] rounded-bl-[8px] w-[fit-content]"
      >
        <div
          class="px-[8px] inline-block text-white bg-[#e50113] rounded-tl-[8px] rounded-tr-[1px] rounded-br-[8px] rounded-bl-[1px] h-[16px]"
        >
          <div class="leading-[16px]" v-if="goods?.supportOnlineOrder">
            {{ authStore.i18n("cm_goods.finalShippingCost") }}
          </div>
          <div class="leading-[16px]" v-else>
            {{ authStore.i18n("cm_goods.shippingCost") }}
          </div>
        </div>
        <br />

        <span class="text-[12px] mt-[4px] ml-[6px]">{{
          setUnit(goods.pcsEstimateFreight)
        }}</span>
        /{{ goods?.goodsPriceUnitName }}
      </div>
    </a>
  </div>
</template>
<script setup lang="ts" name="GoodsCard">
import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();
const props = defineProps({
  goods: {
    type: Object,
    default: () => {},
  },
  imageWidth: {
    type: String,
    default: "188px",
  },
  imageHeight: {
    type: String,
    default: "188px",
  },
});
</script>

<style scoped lang="scss">
.goods-item {
  height: fit-content;
  transition: all 0.3s ease-in-out;
  position: relative;

  &:hover {
    z-index: 2;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
  }
}
</style>
